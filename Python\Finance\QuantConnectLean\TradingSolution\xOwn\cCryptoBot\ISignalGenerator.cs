using QuantConnect;
using QuantConnect.Algorithm;
using QuantConnect.Data.Consolidators;
using QuantConnect.Data.Market;
using QuantConnect.Indicators;

namespace QuantConnect.Algorithm.CSharp
{
    public enum SignalType
    {
        NoSignal = 0,
        SellSignal = 1,
        BuySignal = 2
    }

    public class SignalResult
    {
        public SignalType Signal { get; set; }
        public string Reason { get; set; } = string.Empty;
        public decimal Confidence { get; set; }
    }

    public interface ISignalGenerator
    {
        string Name { get; }
        
        void Initialize(Symbol symbol, TickConsolidator consolidator, QCAlgorithm algorithm);
        
        bool IsReady { get; }
        
        SignalResult GenerateSignal(TradeBar bar, int barIndex);
        
        void Reset();
    }
}
