# import this before anything else to debug `NUMBA`
# fmt: off
import os
os.environ["NUMBA_DISABLE_JIT"] = "0"
# fmt: on

import datetime as dt
import json
import logging
import os
import subprocess
import sys
import time
from cmath import log
from concurrent.futures import thread
from datetime import datetime, timedelta
from io import BytesIO

import alpaca_module
import finance_calendars as fc
import humanfriendly
import pandas as pd
import pendulum
import requests
import random
import tiger_module
import inspect
import numpy as np
from numba import njit, jit, config
from alpaca.common.exceptions import APIError, RetryException

# Only import vectorbtpro if it's installed
try:
    import vectorbtpro as vbt

    VBT_AVAILABLE = True
except ImportError:
    VBT_AVAILABLE = False

LOG_FILE = str()
TRADE_CSV_FILE = str()
QUOTE_CSV_FILE = str()
BAR_CSV_FILE = str()
DELTA_IN_PERCENT = 9.69
PROFIT_IN_PERCENT = 2.16
DELAY_IN_MS_CRITERIA = 3000


def init_logging():
    if VBT_AVAILABLE:
        vbt.settings.set_theme("dark")
        vbt.settings["plotting"]["layout"]["width"] = 1920
        vbt.settings["plotting"]["layout"]["height"] = 1080

    pd.options.display.max_columns = None
    pd.options.display.max_rows = None
    pd.options.display.width = None
    pd.options.display.precision = 10
    pd.options.display.unicode.east_asian_width = True
    np.set_printoptions(threshold=sys.maxsize)

    now_str = datetime.now().strftime("%Y-%m-%d %H-%M-%S")
    log_file_name = "{0}".format(now_str)
    log_dir = os.path.abspath(os.path.join(__file__, os.pardir, os.pardir))
    ubuntu_dir = r"/home/<USER>/Downloads"
    if os.path.isdir(ubuntu_dir):
        log_dir = ubuntu_dir
    log_dir += f"/villa-dbg/{datetime.now().strftime('%Y-%m-%d')}"

    global LOG_FILE
    global TRADE_CSV_FILE
    global QUOTE_CSV_FILE
    global BAR_CSV_FILE
    LOG_FILE = os.path.join(log_dir, log_file_name) + ".txt"
    LOG_FILE = LOG_FILE.replace("\\", "/")
    TRADE_CSV_FILE = os.path.join(log_dir, log_file_name) + "(Trade).txt"
    TRADE_CSV_FILE = TRADE_CSV_FILE.replace("\\", "/")
    QUOTE_CSV_FILE = os.path.join(log_dir, log_file_name) + "(Quote).txt"
    QUOTE_CSV_FILE = QUOTE_CSV_FILE.replace("\\", "/")
    BAR_CSV_FILE = os.path.join(log_dir, log_file_name) + "(Bar).txt"
    BAR_CSV_FILE = BAR_CSV_FILE.replace("\\", "/")

    os.makedirs(os.path.dirname(LOG_FILE), exist_ok=True)
    log_format = "%(asctime)s.%(msecs)03d|%(levelname)-7s|%(funcName)s@%(lineno)d|%(message)s"
    logging.basicConfig(
        handlers=[logging.FileHandler(filename=LOG_FILE, encoding="utf-8", mode="a+")],
        level=logging.INFO,
        format=log_format,
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    logger = logging.getLogger()
    console_logger = logging.StreamHandler()
    console_logger.setFormatter(logging.Formatter(log_format, datefmt="%Y-%m-%d %H:%M:%S"))
    console_logger.setLevel(logging.INFO)
    logger.addHandler(console_logger)

    logging.info("init~")
    caller_frame = inspect.currentframe().f_back
    caller_module = inspect.getmodule(caller_frame)
    caller_module_path = os.path.abspath(caller_module.__file__)
    logging.info(f"caller_module_path: {caller_module_path}")
    logging.info(f"log file: `{LOG_FILE}`")

    # Only get git commit hash if we're in a git repository
    cur_git_dir = os.path.abspath(os.path.join(__file__, os.pardir, os.pardir, os.pardir))
    try:
        current_commit_hash = subprocess.check_output(["git", "-C", f"{cur_git_dir}", "rev-parse", "--short", "HEAD"]).decode("ascii").strip()
        logging.info(f"current git commit short hash: {current_commit_hash}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        logging.info("Not in a git repository or git not available")

    # Only log vectorbt numba status if vectorbt is available
    if VBT_AVAILABLE:
        logging.info(f"vbt.is_numba_enabled: {vbt.is_numba_enabled()}")


def handle_exception(exc_type, exc_value, exc_traceback):
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    logging.error("Uncaught exception", exc_info=(exc_type, exc_value, exc_traceback))


sys.excepthook = handle_exception


def get_aligo_refresh_token():
    token_file = os.path.dirname(os.path.abspath(__file__)) + r"/config/aligo_refresh_token.json"
    with open(token_file) as json_file:
        data = json.load(json_file)
    refresh_token = data["refresh_token"]
    return refresh_token


def get_alpaca_token():
    token_file = os.path.dirname(os.path.abspath(__file__)) + r"/config/alpaca_token.json"
    with open(token_file) as json_file:
        data = json.load(json_file)
    return data["api_key"], data["secret_key"]


@njit(nogil=True)
def get_random_inverted_pyramid(num_parts):
    parts = [0] * num_parts

    remaining_percentage = 100
    for i in range(num_parts - 1):
        upper_limit = remaining_percentage - (num_parts - i - 1)
        parts[i] = random.randint(1, upper_limit)

        remaining_percentage -= parts[i]

    parts[num_parts - 1] = remaining_percentage

    parts.sort(reverse=False)

    return parts


@njit(nogil=True)
def get_ag_inverted_pyramid(num_parts):
    common_difference = 100 / (num_parts * (num_parts + 1) / 2)

    parts = []
    for i in range(num_parts, 0, -1):
        part = common_difference * i
        parts.append(part)

    parts.reverse()
    return parts


@njit(nogil=True)
def get_interval_lists(num_parts):
    parts = []
    for inx in range(num_parts):
        parts.append(100 / num_parts)
    return parts


@njit(nogil=True)
def get_sl_series(n):
    assert n <= 4
    series = [2.69**i for i in range(n + 1)]
    series = series[::-1]
    total_sum = sum(series)

    modified_series = [(100 / total_sum) * item for item in series]
    # assert np.isclose(100, sum(modified_series))
    return modified_series


def log_function_args():
    frame = inspect.currentframe().f_back
    function_name = frame.f_code.co_name
    args, _, _, values = inspect.getargvalues(frame)

    logging.info(f"Function '{function_name}' called with arguments:")
    for arg in args:
        logging.info(f"    {arg} = {values[arg]}")
