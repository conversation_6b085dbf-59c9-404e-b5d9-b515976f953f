using System;
using QuantConnect;
using QuantConnect.Algorithm;
using QuantConnect.Data.Consolidators;
using QuantConnect.Data.Market;
using QuantConnect.Indicators;

namespace QuantConnect.Algorithm.CSharp
{
    public class EMABollingerSignalGenerator : ISignalGenerator
    {
        public string Name => "EMA-Bollinger Signal Generator";

        private const int EMA_FAST_PERIOD = 24;
        private const int EMA_SLOW_PERIOD = 42;
        private const int BOLLINGER_PERIOD = 13;
        private const decimal BOLLINGER_STD_DEV = 1.1m;
        private const int RSI_PERIOD = 6;
        private const int BACK_CANDLES = 7;
        private const int WINDOW_SIZE = 50;

        private ExponentialMovingAverage _emaFast = null!;
        private ExponentialMovingAverage _emaSlow = null!;
        private BollingerBands _bbands = null!;
        private RelativeStrengthIndex _rsi = null!;
        
        private QCAlgorithm _algorithm = null!;
        private Symbol _symbol = null!;

        public bool IsReady => 
            _emaFast?.IsReady == true && 
            _emaSlow?.IsReady == true && 
            _bbands?.IsReady == true && 
            _rsi?.IsReady == true &&
            _emaFast.Window.Count >= BACK_CANDLES + 1 && 
            _emaSlow.Window.Count >= BACK_CANDLES + 1 &&
            _emaSlow.Window.Size > 0;

        public void Initialize(Symbol symbol, TickConsolidator consolidator, QCAlgorithm algorithm)
        {
            _algorithm = algorithm;
            _symbol = symbol;

            _emaFast = new ExponentialMovingAverage($"EMA({EMA_FAST_PERIOD})", EMA_FAST_PERIOD);
            _emaSlow = new ExponentialMovingAverage($"EMA({EMA_SLOW_PERIOD})", EMA_SLOW_PERIOD);
            _bbands = new BollingerBands($"BB({BOLLINGER_PERIOD},{BOLLINGER_STD_DEV})", BOLLINGER_PERIOD, BOLLINGER_STD_DEV, MovingAverageType.Simple);
            _rsi = new RelativeStrengthIndex($"RSI({RSI_PERIOD})", RSI_PERIOD, MovingAverageType.Wilders);

            _emaFast.Window.Size = WINDOW_SIZE;
            _emaSlow.Window.Size = WINDOW_SIZE;
            _bbands.Window.Size = WINDOW_SIZE;
            _bbands.UpperBand.Window.Size = WINDOW_SIZE;
            _bbands.LowerBand.Window.Size = WINDOW_SIZE;
            _bbands.MiddleBand.Window.Size = WINDOW_SIZE;
            _rsi.Window.Size = WINDOW_SIZE;

            algorithm.RegisterIndicator(symbol, _emaFast, consolidator);
            algorithm.RegisterIndicator(symbol, _emaSlow, consolidator);
            algorithm.RegisterIndicator(symbol, _bbands, consolidator);
            algorithm.RegisterIndicator(symbol, _rsi, consolidator);
        }

        public SignalResult GenerateSignal(TradeBar bar, int barIndex)
        {
            if (!IsReady)
            {
                return new SignalResult 
                { 
                    Signal = SignalType.NoSignal, 
                    Reason = "Indicators not ready",
                    Confidence = 0m
                };
            }

            if (barIndex < _emaSlow.Window.Size)
            {
                return new SignalResult 
                { 
                    Signal = SignalType.NoSignal, 
                    Reason = "Insufficient bar history",
                    Confidence = 0m
                };
            }

            bool upTrend = true, downTrend = true;
            
            try 
            {
                for (int j = 0; j < BACK_CANDLES; j++) 
                {
                    var fastValue = _emaFast[j + 1].Value;
                    var slowValue = _emaSlow[j + 1].Value;
                    
                    if (double.IsNaN((double)fastValue) || double.IsNaN((double)slowValue) || fastValue == 0 || slowValue == 0) 
                    {
                        _algorithm.Log($"Invalid indicator values at index {j + 1}: EMAFast={fastValue}, EMASlow={slowValue}");
                        return new SignalResult 
                        { 
                            Signal = SignalType.NoSignal, 
                            Reason = "Invalid indicator values",
                            Confidence = 0m
                        };
                    }
                    
                    upTrend &= fastValue > slowValue;
                    downTrend &= fastValue < slowValue;
                }
            } 
            catch (Exception ex) 
            {
                _algorithm.Log($"Error in trend calculation: {ex.Message}");
                return new SignalResult 
                { 
                    Signal = SignalType.NoSignal, 
                    Reason = "Error in trend calculation",
                    Confidence = 0m
                };
            }

            if (upTrend && bar.Close <= _bbands.LowerBand.Current.Value)
            {
                return new SignalResult 
                { 
                    Signal = SignalType.BuySignal, 
                    Reason = $"EMA uptrend + close <= BBL (close={bar.Close:F5}, BBL={_bbands.LowerBand.Current.Value:F5})",
                    Confidence = 0.8m
                };
            } 
            else if (downTrend && bar.Close >= _bbands.UpperBand.Current.Value) 
            {
                return new SignalResult 
                { 
                    Signal = SignalType.SellSignal, 
                    Reason = $"EMA downtrend + close >= BBU (close={bar.Close:F5}, BBU={_bbands.UpperBand.Current.Value:F5})",
                    Confidence = 0.8m
                };
            }

            return new SignalResult 
            { 
                Signal = SignalType.NoSignal, 
                Reason = "No signal conditions met",
                Confidence = 0m
            };
        }

        public void Reset()
        {
            _emaFast?.Reset();
            _emaSlow?.Reset();
            _bbands?.Reset();
            _rsi?.Reset();
        }
    }
}
