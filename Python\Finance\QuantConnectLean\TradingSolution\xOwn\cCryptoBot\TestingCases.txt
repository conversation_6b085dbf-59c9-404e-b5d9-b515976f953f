1. all 5 StopLoss Orders are filled, might not in sequence
   963, -193, -193, -193, -193, -191
2. all 16 TakeProfit Orders are filled, might not in sequence
   987, -62, -62, -62, -62, -62, -62, -62, -62, -62, -62, -62, -62, -62, -62, -62, -57
3. first 1 StopLoss Orders are filled, then TakeProfit Orders are filled
   (after TakeProfit Orders are filled, the newly created StopLoss Order is filled, in this scenario, when new TakeProfit Order is filled, old StopLoss Order is canceld, new StopLoss Order with higher StopLoss is created.
   2 new StopLoss Orders are created in total)
   986, -197, -62, -62, -665
4. first 4 TakeProfit Orders are filled, resetting StopLoss Orders as one, then filled
   (4 new StopLoss Orders are created in total)
   973, -61, -61, -61, -61, -729
5. first 1 TakeProfit Orders are filled, resetting StopLoss Orders as one, then filled
   (1 new StopLoss Order is created in total)
   968, -62, -924
6. first 4 StopLoss Orders are filled, then TakeProfit Orders are filled, 
    because each Order volume must greater than 1, there are 2 TakeProfit unfilled Orders 62, 56
    but the remaining position is 74, so the last Order 56 must be canceled to avoid postion flipping.
    the one before last Order is 62, we should cancel last 56 Order to create new one with 12. but 12 * 0.05 < 1,
    we have to cancel the last 2 unfilled Orders to create new one with 62 + 12=74
   
   986, -197, -197, -197, -197, -62, -62, -74