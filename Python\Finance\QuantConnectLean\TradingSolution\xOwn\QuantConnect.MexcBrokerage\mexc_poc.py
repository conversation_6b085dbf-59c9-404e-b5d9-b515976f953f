import websockets
import threading
import asyncio
import logging
import json
import time


URI = "wss://contract.mexc.com/edge"

log = logging.getLogger(__name__)


def methods(symbol: str):
    return {"method": "sub.deal", "param": {"symbol": symbol}}, {
        "method": "sub.funding.rate",
        "param": {"symbol": symbol},
    }


async def connect():
    SYMBOLS = [
        "SOL_USDT",
    ]
    while True:
        try:
            async with websockets.connect(URI, ping_interval=None) as web:
                for symbol in SYMBOLS:
                    sub_deal, sub_fund = methods(symbol)
                    await web.send(json.dumps(sub_deal))
                    await web.send(json.dumps(sub_fund))
                start = 0

                while True:
                    try:
                        r = await web.recv()
                        data = json.loads(r)
                        print(data)

                        # ping-pong
                        if time.time() - start > 10:
                            loop = asyncio.get_event_loop()
                            loop.create_task(web.send(json.dumps({"method": "ping"})))
                            start = time.time()
                    except Exception as e:
                        log.error(f"err 1: {e}")
                        break
        except Exception as e:
            log.error(f"err 2: {e}")


asyncio.run(connect())
