import re

def remove_csharp_comments(file_path, output_path=None):
    """
    Remove all comments from a C# file and eliminate blank lines.
    
    Args:
        file_path (str): Path to the input C# file
        output_path (str): Path for output file (optional, defaults to overwriting input)
    """
    
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Remove multi-line comments (/* ... */)
    content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
    
    # Split into lines and process single-line comments
    lines = content.split('\n')
    processed_lines = []
    
    for line in lines:
        # Remove single-line comments (//)
        # Handle string literals to avoid removing // inside strings
        in_string = False
        escape_next = False
        comment_start = -1
        
        i = 0
        while i < len(line):
            char = line[i]
            
            if escape_next:
                escape_next = False
                i += 1
                continue
                
            if char == '\\' and in_string:
                escape_next = True
                i += 1
                continue
                
            if char == '"' and not escape_next:
                in_string = not in_string
                i += 1
                continue
                
            if not in_string and char == '/' and i + 1 < len(line) and line[i + 1] == '/':
                comment_start = i
                break
                
            i += 1
        
        # Remove comment part if found
        if comment_start != -1:
            line = line[:comment_start].rstrip()
        
        # Only keep lines that have content after removing comments
        if line.strip():
            processed_lines.append(line)
    
    # Join lines back together
    cleaned_content = '\n'.join(processed_lines)
    
    # Write to output file
    output_file = output_path if output_path else file_path
    with open(output_file, 'w', encoding='utf-8') as file:
        file.write(cleaned_content)
    
    print(f"Comments removed successfully. Output saved to: {output_file}")

# Example usage
if __name__ == "__main__":
    # Remove comments from a C# file
    input_file = r'D:\work\xstarwalker168\Python\Finance\QuantConnectLean\TradingSolution\xOwn\QuantConnect.MexcBrokerage\MexcBrokerageFactory.cs'
    
    # Option 1: Overwrite the original file
    remove_csharp_comments(input_file)
    
    # Option 2: Save to a new file
    # remove_csharp_comments(input_file, "cleaned_example.cs")