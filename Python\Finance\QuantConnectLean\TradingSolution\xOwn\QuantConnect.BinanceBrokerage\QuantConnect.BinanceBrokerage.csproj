﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Release</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <TargetFramework>net9.0</TargetFramework>
    <Product>QuantConnect.Brokerages.Binance</Product>
    <AssemblyName>QuantConnect.Brokerages.Binance</AssemblyName>
    <RootNamespace>QuantConnect.Brokerages.Binance</RootNamespace>
    <AssemblyTitle>QuantConnect.Brokerages.Binance</AssemblyTitle>
    <OutputType>Library</OutputType>
    <OutputPath>bin\$(Configuration)\</OutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <Description>QuantConnect LEAN Binance Brokerage: Brokerage Binance plugin for Lean</Description>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugType>full</DebugType>
    <OutputPath>bin\Debug\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <OutputPath>bin\Release\</OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Lean\Brokerages\QuantConnect.Brokerages.csproj" />
  </ItemGroup>
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="xcopy /Y &quot;$(TargetPath)&quot; $(SolutionDir)Launcher\bin\$(Configuration)\&#xD;&#xA;&#xD;&#xA;if exist &quot;D:\work\xstarwalker168\Python\Finance\QuantConnectLean\QC-Log-Dir&quot; del /q &quot;D:\work\xstarwalker168\Python\Finance\QuantConnectLean\QC-Log-Dir\*&quot;&#xD;&#xA;&#xD;&#xA;" />
  </Target>
</Project>